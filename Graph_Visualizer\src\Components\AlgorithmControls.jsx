import { useState, useRef } from 'react';
import Bfs from '../Algorithms/Bfs';
import Dfs from '../Algorithms/dfs';

const AlgorithmControls = ({ nodes, setNodes, links, setLinks, adjList, setResult, setResultReady }) => {
    const [selectedAlgorithm, setSelectedAlgorithm] = useState('bfs');
    const [startNode, setStartNode] = useState('');
    const [isRunning, setIsRunning] = useState(false);
    const speedRef = useRef('normal');

    const handleRunAlgorithm = async () => {
        if (!startNode || !adjList[startNode]) {
            alert('Please enter a valid start node');
            return;
        }

        setIsRunning(true);
        setResult('');
        setResultReady(false);

        const delay = speedRef.current === 'fast' ? 100 : speedRef.current === 'slow' ? 1000 : 500;

        try {
            if (selectedAlgorithm === 'bfs') {
                await Bfs(startNode, nodes, setNodes, adjList, delay, speedRef, links, setLinks);
                setResult(`BFS traversal completed starting from node ${startNode}`);
            } else if (selectedAlgorithm === 'dfs') {
                await Dfs(startNode, nodes, setNodes, adjList, delay, speedRef, links, setLinks);
                setResult(`DFS traversal completed starting from node ${startNode}`);
            }
            setResultReady(true);
        } catch (error) {
            console.error('Algorithm execution error:', error);
            setResult(`Error running ${selectedAlgorithm.toUpperCase()}: ${error.message}`);
        }

        setIsRunning(false);
    };

    const handleSpeedChange = (speed) => {
        speedRef.current = speed;
    };

    const nodeIds = nodes.map(node => node.id);

    return (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md mb-4">
            <h3 className="text-lg font-semibold mb-4">Algorithm Controls</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label className="block text-sm font-medium mb-2">Algorithm</label>
                    <select 
                        value={selectedAlgorithm}
                        onChange={(e) => setSelectedAlgorithm(e.target.value)}
                        className="w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600"
                        disabled={isRunning}
                    >
                        <option value="bfs">Breadth-First Search (BFS)</option>
                        <option value="dfs">Depth-First Search (DFS)</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium mb-2">Start Node</label>
                    <select 
                        value={startNode}
                        onChange={(e) => setStartNode(e.target.value)}
                        className="w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600"
                        disabled={isRunning}
                    >
                        <option value="">Select start node</option>
                        {nodeIds.map(nodeId => (
                            <option key={nodeId} value={nodeId}>{nodeId}</option>
                        ))}
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium mb-2">Speed</label>
                    <select 
                        onChange={(e) => handleSpeedChange(e.target.value)}
                        className="w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600"
                        disabled={isRunning}
                    >
                        <option value="slow">Slow</option>
                        <option value="normal">Normal</option>
                        <option value="fast">Fast</option>
                    </select>
                </div>

                <div className="flex items-end">
                    <button 
                        onClick={handleRunAlgorithm}
                        disabled={isRunning || !startNode}
                        className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded font-medium"
                    >
                        {isRunning ? 'Running...' : 'Run Algorithm'}
                    </button>
                </div>
            </div>

            <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                <p><span className="inline-block w-3 h-3 bg-blue-500 rounded-full mr-2"></span>Unvisited</p>
                <p><span className="inline-block w-3 h-3 bg-orange-500 rounded-full mr-2"></span>Currently Visiting</p>
                <p><span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>Visited</p>
            </div>
        </div>
    );
};

export default AlgorithmControls;
