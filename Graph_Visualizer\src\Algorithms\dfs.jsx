import Reset from "../Components/ResetColors";

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function Dfs(src, prevNodes, onNodeChange, adjList, delay, speedrun, links, onLinkChange) {
    
    const[resetNodes, resetLinks] = Reset(prevNodes, links);
    onNodeChange(resetNodes);
    onLinkChange(resetLinks);
    prevNodes = resetNodes;

    const visited = new Set();

    async function dfs(nodeId) {
        if(visited.has(nodeId)) return;
        visited.add(nodeId);

        // Mark node as visiting (orange)

        const updated = prevNodes.map(n => {
            if(n.id == nodeId){
                n.color = 'orange';
            }
            return n;
        });
        onNodeChange(updated);

        if(speedrun.current == 'fast'){
            delay = 100;
        }else if(speedrun.current == 'skip'){
            delay = 0;
        }

        await(delay);

        // Visit neighbors

        for(const neighbor of adjList[nodeId] || []){
            await dfs(neighbor);
        }

        // Mark node as visited (green)

        const final = updated.map(n => {
            if(n.id == nodeId){
                n.color = 'green';
            }
            return n;
        });

        onNodeChange(final);

        await sleep(delay);
    }
    await dfs(src);
}

export default Dfs;