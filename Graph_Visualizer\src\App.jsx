import {use, useEffect, useState} from 'react';
import './App.css';

import parseGraphInput from './Hooks/DataFetch';
import Bfs from './Algorithms/Bfs';
import Dfs from './Algorithms/dfs';
import buildAdjList from './Components/BuildAdjList';




const App = () => {

  const [nodes, setNodes] = useState([]);
  const [links, setLinks] = useState([]);
  const [adjList, setAdjList] = useState({});
  const [isDirected, setIsDirected] = useState(false);
  const [inputText, setInputText] = useState("");
  const [cycleDirected, setCycleDirected] = useState(false);
  const [weighted, setWeighted] = useState(false);
  const [result, setResult] = useState("");
  const [resultReady, setResultReady] = useState(false);

  useEffect(() => {
    const{nodes, links, adjList} = parseGraphInput(inputText, isDirected, weighted);
    setNodes(nodes);
    setLinks(links);
    setAdjList(adjList);
  }, [inputText, isDirected, weighted]);

  const handleInputChange = (e) => {
    setInputText(e.target.value);
  };

  return (
    <div className='min-h-screen bg-gray-200 dark:bg-gray-950 text-gray-900 dark:text-gray-100'>
      <div className='container mx-auto p-4 bg-gray-300 dark:bg-gray-900 rounded-xl shadow-md'>
        <input 
          onInputChange = {handleInputChange}
          nodes = {nodes}
          setNodes = {setNodes}
          setIsDirected = {setIsDirected}
          isDirected = {isDirected}
          adjList = {adjList}
          isWeighted = {isWeighted}
          setIsWeighted = {setIsWeighted}
          links = {links}
          setLinksData = {setLinks}
          result={result}
          setResult = {setResult}
          setResultReady = {setResultReady}
         />

         <div className='graphSection bg-gray-400 dark:bg-gray-900 rounded-xl shadow-md h-max'>
          {resultReady && (
            <div className='mt-2 text-green-400 font-medium absolute'>
              ✅ result is ready! You can see by clicking on below show result button!
            </div>
          )}

          <graphVisualizer
            nodesData = {nodes}
            linksData = {links}
            isDirected = {isDirected}
            isWeighted = {isWeighted}
          />

          <resultModal result = {result} />

         </div>
      </div>
    </div>
    
  );
}

export default App