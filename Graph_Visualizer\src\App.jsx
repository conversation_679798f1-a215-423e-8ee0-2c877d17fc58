import {useEffect, useState} from 'react';
import './App.css';

import parseGraphInput from './Hooks/DataFetch';
import GraphVisualizer from './Components/GraphVisualizer';
import AlgorithmControls from './Components/AlgorithmControls';
import ExampleData from './Components/ExampleData';




const App = () => {

  const [nodes, setNodes] = useState([]);
  const [links, setLinks] = useState([]);
  const [adjList, setAdjList] = useState({});
  const [isDirected, setIsDirected] = useState(false);
  const [inputText, setInputText] = useState("");
  const [isWeighted, setIsWeighted] = useState(false);
  const [result, setResult] = useState("");
  const [resultReady, setResultReady] = useState(false);

  useEffect(() => {
    const{nodes, links, adjList} = parseGraphInput(inputText, isDirected, isWeighted);
    setNodes(nodes);
    setLinks(links);
    setAdjList(adjList);
  }, [inputText, isDirected, isWeighted]);

  const handleInputChange = (e) => {
    setInputText(e.target.value);
  };

  return (
    <div className='min-h-screen bg-gray-200 dark:bg-gray-950 text-gray-900 dark:text-gray-100'>
      <div className='container mx-auto p-4 bg-gray-300 dark:bg-gray-900 rounded-xl shadow-md'>
        <div className="mb-4">
          <textarea
            value={inputText}
            onChange={handleInputChange}
            placeholder="Enter graph edges (e.g., A B or A B 5 for weighted)"
            className="w-full p-3 border rounded-lg dark:bg-gray-800 dark:border-gray-600"
            rows="4"
          />
          <div className="mt-2 flex gap-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isDirected}
                onChange={(e) => setIsDirected(e.target.checked)}
                className="mr-2"
              />
              Directed Graph
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isWeighted}
                onChange={(e) => setIsWeighted(e.target.checked)}
                className="mr-2"
              />
              Weighted Graph
            </label>
          </div>
        </div>

        <ExampleData
          setInputText={setInputText}
          setIsDirected={setIsDirected}
          setIsWeighted={setIsWeighted}
        />

        {nodes.length > 0 && (
          <AlgorithmControls
            nodes={nodes}
            setNodes={setNodes}
            links={links}
            setLinks={setLinks}
            adjList={adjList}
            setResult={setResult}
            setResultReady={setResultReady}
          />
        )}

         <div className='graphSection bg-gray-100 dark:bg-gray-800 rounded-xl shadow-md p-4'>
          {resultReady && (
            <div className='mb-4 text-green-400 font-medium'>
              ✅ Result is ready! You can see by clicking on below show result button!
            </div>
          )}

          <GraphVisualizer
            nodesData={nodes}
            linksData={links}
            isDirected={isDirected}
            isWeighted={isWeighted}
          />

          {result && (
            <div className="mt-4 p-4 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <h3 className="font-bold mb-2">Algorithm Result:</h3>
              <pre className="whitespace-pre-wrap">{result}</pre>
            </div>
          )}

         </div>
      </div>
    </div>
    
  );
}

export default App