import { useEffect, useRef } from "react";
import * as d3 from "d3";

const GraphVisualizer = ({nodesData, linksData, isDirected, isWeighted}) => {
    const svgRef = useRef();

    useEffect(() => {
        const width = 800;
        const height = 800;
        const radius = window.innerWidth < 768 ? 25 : 15;
        const isMobile = window.innerWidth < 768;
        const fontSize = isMobile ? "20px" : "12px";

        const svg = d3
            .select(svgRef.current)
            .attr("viewBox", `0 0 ${width} ${height}`)
            .attr("preserveAspectRatio", "xMidYMid meet")
            .classed(
                "w-full h-200 bg-white dark:bg-gray-800 rounded-2xl shadow",
                true
            );
        
        svg.selectAll("*").remove(); // Clear before re-render

        // Add arrow marker (used conditionally)
        svg
            .append("defs")
            .append("marker")
            .attr("id", "arrow")
            .attr("viewBox", "0 -5 10 10")
            .attr("refx", 20)
            .attr("refY", 0)
            .attr("markerWidth", 6)
            .attr("markerHeight", 6)
            .attr("orient", "auto")
            .append("path")
            .attr("d", "M0,-5L10,0L0,5")
            .attr("fill", "#999");
        
        const simulation = d3
            
    })
}
