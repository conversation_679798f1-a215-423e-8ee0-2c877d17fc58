import { useEffect, useRef } from "react";
import * as d3 from "d3";

const GraphVisualizer = ({nodesData, linksData, isDirected, isWeighted}) => {
    const svgRef = useRef();

    useEffect(() => {
        const width = 800;
        const height = 800;
        const radius = window.innerWidth < 768 ? 25 : 15;
        const isMobile = window.innerWidth < 768;
        const fontSize = isMobile ? "20px" : "12px";

        const svg = d3
            .select(svgRef.current)
            .attr("viewBox", `0 0 ${width} ${height}`)
            .attr("preserveAspectRatio", "xMidYMid meet")
            .classed(
                "w-full h-200 bg-white dark:bg-gray-800 rounded-2xl shadow",
                true
            );
        
        svg.selectAll("*").remove(); // Clear before re-render

        // Add arrow marker (used conditionally)
        svg
            .append("defs")
            .append("marker")
            .attr("id", "arrow")
            .attr("viewBox", "0 -5 10 10")
            .attr("refx", 20)
            .attr("refY", 0)
            .attr("markerWidth", 6)
            .attr("markerHeight", 6)
            .attr("orient", "auto")
            .append("path")
            .attr("d", "M0,-5L10,0L0,5")
            .attr("fill", "#999");
        
        const simulation = d3
            .forceSimulation(nodesData)
            .force("link", d3.forceLink(linksData).id(d => d.id).distance(100))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2));

        // Create links
        const link = svg
            .append("g")
            .attr("class", "links")
            .selectAll("line")
            .data(linksData)
            .enter()
            .append("line")
            .attr("stroke", d => d.color || "#999")
            .attr("stroke-width", 2)
            .attr("marker-end", isDirected ? "url(#arrow)" : null);

        // Create nodes
        const node = svg
            .append("g")
            .attr("class", "nodes")
            .selectAll("circle")
            .data(nodesData)
            .enter()
            .append("circle")
            .attr("r", radius)
            .attr("fill", d => d.color || "#4f46e5")
            .attr("stroke", "#fff")
            .attr("stroke-width", 2)
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        // Add node labels
        const labels = svg
            .append("g")
            .attr("class", "labels")
            .selectAll("text")
            .data(nodesData)
            .enter()
            .append("text")
            .text(d => d.id)
            .attr("font-size", fontSize)
            .attr("font-family", "Arial, sans-serif")
            .attr("fill", "#333")
            .attr("text-anchor", "middle")
            .attr("dy", ".35em");

        // Add edge labels for weighted graphs
        let edgeLabels;
        if (isWeighted) {
            edgeLabels = svg
                .append("g")
                .attr("class", "edge-labels")
                .selectAll("text")
                .data(linksData)
                .enter()
                .append("text")
                .text(d => d.weight)
                .attr("font-size", "10px")
                .attr("font-family", "Arial, sans-serif")
                .attr("fill", "#666")
                .attr("text-anchor", "middle");
        }

        // Update positions on simulation tick
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);

            labels
                .attr("x", d => d.x)
                .attr("y", d => d.y);

            if (isWeighted && edgeLabels) {
                edgeLabels
                    .attr("x", d => (d.source.x + d.target.x) / 2)
                    .attr("y", d => (d.source.y + d.target.y) / 2);
            }
        });

        // Drag functions
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

    }, [nodesData, linksData, isDirected, isWeighted]);

    return (
        <div className="w-full">
            <svg ref={svgRef} className="w-full h-96 bg-white dark:bg-gray-800 rounded-lg shadow"></svg>
        </div>
    );
};

export default GraphVisualizer;
